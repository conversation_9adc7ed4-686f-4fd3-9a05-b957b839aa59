import type { Metada<PERSON> } from "next";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";
import { cn } from "@/lib/utils";
import { FONT_LATO } from "@/constants/fonts";

export const revalidate = 30;

export const metadata: Metadata = {
  metadataBase: new URL("https://www.lindamensah.com/"),
  title: {
    template: "%s | <PERSON>",
    default: "<PERSON> - Professional Portfolio",
  },
  description:
    "Professional portfolio of <PERSON> - Experienced leader in project management, strategic planning, and organizational development. Available for consulting and collaboration opportunities.",
  openGraph: {
    title: "<PERSON> - Professional Portfolio",
    description:
      "Professional portfolio of <PERSON> - Experienced leader in project management, strategic planning, and organizational development. Available for consulting and collaboration opportunities.",
    url: "https://www.lindamensah.com/",
    siteName: "Linda Mensah Portfolio",
    locale: "en-US",
    type: "website",
  },
  robots: {
    index: true,
    follow: true,
    nocache: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
  icons: {
    shortcut: "/favicon-16x16.png",
    apple: "/public/apple-touch-icon.png",
    other: {
      rel: "apple-touch-icon-precomposed",
      url: "/public/android-chrome-192x192.png",
    },
  },
  twitter: {
    card: "summary_large_image",
    title: "Black Cherry",
    description:
      "Cosmetic Consultancy, Beauty Services, Training Services on Cosmetic.",
    site: "https://www.blackcherrygh.com/",
  },
};
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={cn(
          FONT_LATO.className,
          "text-lg tracking-wide text-neutral-600",
        )}
      >
        {children}
        <Toaster />
      </body>
    </html>
  );
}
