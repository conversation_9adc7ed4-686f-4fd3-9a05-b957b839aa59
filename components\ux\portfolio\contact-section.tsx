"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { aldineBT, fibonSans } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import { CVData } from "@/lib/cv-content";
import { motion } from "framer-motion";
import { 
  Mail, 
  Phone, 
  MapPin, 
  Linkedin, 
  Send,
  MessageSquare,
  Clock,
  CheckCircle
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface ContactSectionProps {
  cvData: CVData;
}

export default function ContactSection({ cvData }: ContactSectionProps) {
  const { personalInfo } = cvData;
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
  };

  const contactMethods = [
    {
      icon: Mail,
      title: "Email",
      value: personalInfo.email,
      href: `mailto:${personalInfo.email}`,
      description: "Send me an email anytime"
    },
    {
      icon: Phone,
      title: "Phone",
      value: personalInfo.phone,
      href: `tel:${personalInfo.phone}`,
      description: "Call for immediate response"
    },
    {
      icon: Linkedin,
      title: "LinkedIn",
      value: "Connect with me",
      href: `https://${personalInfo.linkedin}`,
      description: "Professional networking"
    },
    {
      icon: MapPin,
      title: "Location",
      value: personalInfo.location,
      href: "#",
      description: "Based in Ghana"
    }
  ];

  const faqs = [
    {
      question: "What services do you offer?",
      answer: "I provide consulting services in project management, strategic planning, and organizational development. I also offer training and mentoring for professional development."
    },
    {
      question: "What is your availability for new projects?",
      answer: "I'm currently accepting new projects and consulting opportunities. My availability varies based on project scope and timeline. Please reach out to discuss your specific needs."
    },
    {
      question: "Do you work with remote teams?",
      answer: "Yes, I have extensive experience working with distributed teams and remote collaboration. I'm comfortable with various digital tools and platforms for effective remote work."
    },
    {
      question: "What industries do you have experience in?",
      answer: "I have worked across multiple industries including technology, finance, healthcare, and education. My diverse background allows me to bring cross-industry insights to new challenges."
    }
  ];

  return (
    <section id="contact" className="py-20 bg-slate-50">
      <div className="max-w-7xl mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className={cn(aldineBT.className, "text-4xl lg:text-5xl font-bold text-slate-900 mb-6")}>
            Let's Work Together
          </h2>
          <div className="w-24 h-1 bg-blue-600 mx-auto mb-8" />
          <p className={cn(fibonSans.className, "text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed")}>
            Ready to discuss your next project or explore collaboration opportunities? 
            I'd love to hear from you and learn how I can contribute to your success.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-white rounded-2xl p-8 shadow-lg"
          >
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <MessageSquare className="h-5 w-5 text-white" />
              </div>
              <h3 className={cn(aldineBT.className, "text-2xl font-bold text-slate-900")}>
                Send a Message
              </h3>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className={cn(fibonSans.className, "block text-sm font-medium text-slate-700 mb-2")}>
                    Your Name
                  </label>
                  <Input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter your name"
                    className="w-full"
                    required
                  />
                </div>
                <div>
                  <label className={cn(fibonSans.className, "block text-sm font-medium text-slate-700 mb-2")}>
                    Email Address
                  </label>
                  <Input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter your email"
                    className="w-full"
                    required
                  />
                </div>
              </div>

              <div>
                <label className={cn(fibonSans.className, "block text-sm font-medium text-slate-700 mb-2")}>
                  Subject
                </label>
                <Input
                  type="text"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  placeholder="What's this about?"
                  className="w-full"
                  required
                />
              </div>

              <div>
                <label className={cn(fibonSans.className, "block text-sm font-medium text-slate-700 mb-2")}>
                  Message
                </label>
                <Textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  placeholder="Tell me about your project or inquiry..."
                  rows={6}
                  className="w-full"
                  required
                />
              </div>

              <Button
                type="submit"
                size="lg"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Send className="h-4 w-4 mr-2" />
                Send Message
              </Button>
            </form>
          </motion.div>

          {/* Contact Information & FAQs */}
          <div className="space-y-8">
            {/* Contact Methods */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl p-8 shadow-lg"
            >
              <h3 className={cn(aldineBT.className, "text-2xl font-bold text-slate-900 mb-6")}>
                Get In Touch
              </h3>
              
              <div className="space-y-4">
                {contactMethods.map((method, index) => (
                  <motion.div
                    key={method.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <Link
                      href={method.href}
                      className="flex items-center space-x-4 p-4 rounded-lg hover:bg-slate-50 transition-colors group"
                    >
                      <div className="w-10 h-10 bg-blue-100 group-hover:bg-blue-600 rounded-lg flex items-center justify-center transition-colors">
                        <method.icon className="h-5 w-5 text-blue-600 group-hover:text-white transition-colors" />
                      </div>
                      <div className="flex-1">
                        <h4 className={cn(fibonSans.className, "font-semibold text-slate-900")}>
                          {method.title}
                        </h4>
                        <p className={cn(fibonSans.className, "text-blue-600 font-medium")}>
                          {method.value}
                        </p>
                        <p className={cn(fibonSans.className, "text-sm text-slate-600")}>
                          {method.description}
                        </p>
                      </div>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Response Time */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-gradient-to-r from-blue-600 to-slate-700 rounded-2xl p-6 text-white"
            >
              <div className="flex items-center space-x-3 mb-4">
                <Clock className="h-6 w-6" />
                <h4 className={cn(aldineBT.className, "text-xl font-bold")}>
                  Quick Response
                </h4>
              </div>
              <p className={cn(fibonSans.className, "text-blue-100")}>
                I typically respond to inquiries within 24 hours. For urgent matters, 
                please call directly or mention "urgent" in your message subject.
              </p>
            </motion.div>
          </div>
        </div>

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <h3 className={cn(aldineBT.className, "text-3xl font-bold text-slate-900 mb-12 text-center")}>
            Frequently Asked Questions
          </h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-xl p-6 shadow-lg"
              >
                <div className="flex items-start space-x-3 mb-3">
                  <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0 mt-1" />
                  <h4 className={cn(fibonSans.className, "font-bold text-slate-900")}>
                    {faq.question}
                  </h4>
                </div>
                <p className={cn(fibonSans.className, "text-slate-600 leading-relaxed ml-8")}>
                  {faq.answer}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
