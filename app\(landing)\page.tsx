// import { Brands } from "@/components/ux/home/<USER>";
// import FAQS from "@/components/ux/home/<USER>";
// import HomeHero from "@/components/ux/home/<USER>";
// import OtherSections from "@/components/ux/home/<USER>";
// import { FounderSection } from "@/components/ux/home/<USER>";
// import {
//   getAllSeyNaturelleProducts,
//   getImageComponentBySlug,
// } from "@/lib/sanity/lib/actions";
// import { imageComponent, seyNaturelleProductdType } from "@/types/types";
import aL from "@/public/images/pc-l.png";
import Image from "next/image";
import { Noise } from "@/components/ux/animations/noise";

export const revalidate = 30;

export default async function Home() {
  // const imageComponent: imageComponent =
  //   await getImageComponentBySlug("home-carousel");

  // const seyNaturelleProducts: seyNaturelleProductdType[] =
  //   await getAllSeyNaturelleProducts();

  return (
    <main>
      <section className="relative z-10 flex h-[44rem] w-full flex-col items-center justify-center overflow-hidden">
        <Noise />
        <Image
          alt="home"
          src={aL}
          className="absolute inset-0 z-0 h-[44rem] object-cover object-[80%] md:object-left"
        />
        div
      </section>
    </main>
  );
}
