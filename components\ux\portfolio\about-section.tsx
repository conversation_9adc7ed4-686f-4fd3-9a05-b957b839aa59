"use client";

import { aldineBT, fibonSans } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import { CVData } from "@/lib/cv-content";
import { motion } from "framer-motion";
import { Award, BookOpen, Briefcase, Users } from "lucide-react";

interface AboutSectionProps {
  cvData: CVData;
}

export default function AboutSection({ cvData }: AboutSectionProps) {
  const { workExperience, education, awards, professionalSummary } = cvData;

  const stats = [
    {
      icon: Briefcase,
      value: `${workExperience.length}+`,
      label: "Years Experience",
      description: "Professional roles"
    },
    {
      icon: BookOpen,
      value: `${education.length}`,
      label: "Degrees",
      description: "Educational qualifications"
    },
    {
      icon: Award,
      value: `${awards.length}+`,
      label: "Awards",
      description: "Professional recognition"
    },
    {
      icon: Users,
      value: "50+",
      label: "Projects",
      description: "Successfully completed"
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className={cn(aldineBT.className, "text-4xl lg:text-5xl font-bold text-slate-900 mb-6")}>
            About Linda
          </h2>
          <div className="w-24 h-1 bg-blue-600 mx-auto mb-8" />
          <p className={cn(fibonSans.className, "text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed")}>
            A dedicated professional committed to excellence, innovation, and making a meaningful impact 
            through strategic thinking and collaborative leadership.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Story */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <h3 className={cn(aldineBT.className, "text-3xl font-bold text-slate-900 mb-6")}>
              Professional Journey
            </h3>
            
            <div className="space-y-4">
              <p className={cn(fibonSans.className, "text-lg text-slate-700 leading-relaxed")}>
                {professionalSummary}
              </p>
              
              <p className={cn(fibonSans.className, "text-lg text-slate-700 leading-relaxed")}>
                With a strong foundation in {education[0]?.degree || "her field"} and extensive experience 
                across diverse industries, Linda brings a unique perspective to every challenge. Her approach 
                combines analytical rigor with creative problem-solving to deliver sustainable solutions.
              </p>
              
              <p className={cn(fibonSans.className, "text-lg text-slate-700 leading-relaxed")}>
                Throughout her career, Linda has demonstrated exceptional leadership capabilities, 
                successfully managing cross-functional teams and driving organizational transformation. 
                Her commitment to continuous learning and professional development ensures she stays 
                at the forefront of industry best practices.
              </p>
            </div>

            {/* Core Values */}
            <div className="mt-8">
              <h4 className={cn(aldineBT.className, "text-xl font-semibold text-slate-900 mb-4")}>
                Core Values
              </h4>
              <div className="grid grid-cols-2 gap-4">
                {["Excellence", "Innovation", "Integrity", "Collaboration"].map((value, index) => (
                  <motion.div
                    key={value}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-slate-50 rounded-lg p-4 text-center"
                  >
                    <span className={cn(fibonSans.className, "font-medium text-slate-800")}>
                      {value}
                    </span>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Right Column - Stats */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <h3 className={cn(aldineBT.className, "text-3xl font-bold text-slate-900 mb-8")}>
              Professional Highlights
            </h3>
            
            <div className="grid grid-cols-2 gap-6">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-gradient-to-br from-blue-50 to-slate-50 rounded-2xl p-6 text-center hover:shadow-lg transition-shadow"
                >
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-600 rounded-xl mb-4">
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className={cn(aldineBT.className, "text-3xl font-bold text-slate-900 mb-2")}>
                    {stat.value}
                  </div>
                  <div className={cn(fibonSans.className, "font-semibold text-slate-800 mb-1")}>
                    {stat.label}
                  </div>
                  <div className={cn(fibonSans.className, "text-sm text-slate-600")}>
                    {stat.description}
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Recent Achievement Highlight */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-gradient-to-r from-blue-600 to-slate-700 rounded-2xl p-6 text-white"
            >
              <h4 className={cn(aldineBT.className, "text-xl font-bold mb-3")}>
                Recent Achievement
              </h4>
              <p className={cn(fibonSans.className, "text-blue-100 leading-relaxed")}>
                {awards[0]?.name || "Excellence in Leadership Award"} - 
                Recognized for outstanding contribution to organizational success and team development.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
